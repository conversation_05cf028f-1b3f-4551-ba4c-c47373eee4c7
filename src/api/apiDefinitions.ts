/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * Swagger Petstore - OpenAPI 3.0 - version 1.0.27
 *
 * This is a sample Pet Store Server based on the OpenAPI 3.0 specification.  You can find out more about
Swagger at [https://swagger.io](https://swagger.io). In the third iteration of the pet store, we&#x27;ve switched to the design first approach!
You can now help us improve the API whether it&#x27;s by making changes to the definition itself or to the code.
That way, with time, we can improve the API in general, and expose some of the new features in OAS3.

Some useful links:
- [The Pet Store repository](https://github.com/swagger-api/swagger-petstore)
- [The source API definition for the Pet Store](https://github.com/swagger-api/swagger-petstore/blob/master/src/main/resources/openapi.yaml)
 *
 * OpenAPI version: 3.0.4
 *
 * Contact: 
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'pet.updatePet': ['PUT', '/pet'],
  'pet.addPet': ['POST', '/pet'],
  'pet.findPetsByStatus': ['GET', '/pet/findByStatus'],
  'pet.findPetsByTags': ['GET', '/pet/findByTags'],
  'pet.getPetById': ['GET', '/pet/{petId}'],
  'pet.updatePetWithForm': ['POST', '/pet/{petId}'],
  'pet.deletePet': ['DELETE', '/pet/{petId}'],
  'pet.uploadFile': ['POST', '/pet/{petId}/uploadImage'],
  'store.getInventory': ['GET', '/store/inventory'],
  'store.placeOrder': ['POST', '/store/order'],
  'store.getOrderById': ['GET', '/store/order/{orderId}'],
  'store.deleteOrder': ['DELETE', '/store/order/{orderId}'],
  'user.createUser': ['POST', '/user'],
  'user.createUsersWithListInput': ['POST', '/user/createWithList'],
  'user.loginUser': ['GET', '/user/login'],
  'user.logoutUser': ['GET', '/user/logout'],
  'user.getUserByName': ['GET', '/user/{username}'],
  'user.updateUser': ['PUT', '/user/{username}'],
  'user.deleteUser': ['DELETE', '/user/{username}']
};
