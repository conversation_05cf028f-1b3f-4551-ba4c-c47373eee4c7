---
description:
globs:
alwaysApply: false
---
# 开发工作流规则

## 开发环境

### 启动开发服务器
```bash
# H5开发
pnpm dev
pnpm dev:h5

# 小程序开发
pnpm dev:mp-weixin    # 微信小程序
pnpm dev:mp-alipay    # 支付宝小程序
pnpm dev:mp-baidu     # 百度小程序

# App开发
pnpm dev:app          # App
pnpm dev:app-android  # Android
pnpm dev:app-ios      # iOS
```

### 代码生成
```bash
# 生成API接口
pnpm alova-gen        # 生成API
pnpm alova-gen -f     # 强制重新生成
```

## 代码规范

### ESLint配置
- 基于 `@uni-helper/eslint-config`
- 支持 Vue 3 + TypeScript
- 集成 UnoCSS 规则

### 代码检查命令
```bash
pnpm lint           # 检查代码规范
pnpm lint:fix       # 自动修复代码规范问题
pnpm type-check     # TypeScript类型检查
```

### Git工作流
1. **提交前检查**: husky + lint-staged 自动检查代码
2. **提交信息规范**: 使用 commitizen
   ```bash
   pnpm commit         # 规范化提交
   # 或者
   git-cz
   ```
3. **提交信息格式**: 遵循 conventional commits
   - `feat:` 新功能
   - `fix:` 修复bug
   - `docs:` 文档更新
   - `style:` 代码格式
   - `refactor:` 代码重构
   - `test:` 测试相关
   - `chore:` 其他修改

### 版本发布
```bash
pnpm release-patch    # 补丁版本 (1.0.0 -> 1.0.1)
pnpm release-minor    # 次版本 (1.0.0 -> 1.1.0)
pnpm release-major    # 主版本 (1.0.0 -> 2.0.0)
```

## 构建部署

### 构建命令
```bash
# H5构建
pnpm build
pnpm build:h5
pnpm build:h5:ssr     # SSR构建

# 小程序构建
pnpm build:mp-weixin
pnpm build:mp-alipay

# App构建
pnpm build:app
pnpm build:app-android
pnpm build:app-ios
```

### 构建产物
- H5: `dist/build/h5/`
- 小程序: `dist/build/mp-weixin/` 等
- App: `dist/build/app/`

## 开发最佳实践

### 文件组织
1. 新页面在 `src/pages/` 下创建目录和 `index.vue`
2. 复用组件放在 `src/components/`
3. 业务逻辑封装为 composables
4. API接口统一通过 Alova 管理

### 性能优化
1. 合理使用组件懒加载
2. 图片资源优化和压缩
3. 避免过度使用响应式数据
4. 合理配置API缓存策略

### 调试技巧
1. 使用 Vue DevTools 调试组件状态
2. 利用 uni-app 开发者工具调试小程序
3. 使用 console.log 和断点调试
4. 关注网络请求和性能监控

### 跨平台兼容
1. 使用 uni-app 条件编译处理平台差异
2. 样式使用 rpx 单位适配不同屏幕
3. API调用注意平台兼容性
4. 测试覆盖主要目标平台
