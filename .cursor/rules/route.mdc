---
description:
globs:
alwaysApply: false
---
# 路由开发规则

## 基于文件的路由
1. 本项目基于@uni-helper/vite-plugin-uni-pages在uni-app 上实现基于文件的路由系统。
2. 使用SFC 自定义块用于路由数据，通过添加一个 <route> 块到 SFC 中来添加路由元数据。这将会在路由生成后直接添加到路由中，并且会覆盖。
3. 在 `pages.config.ts` 文件中可以配置 `tabbar` 等全局配置。

## 布局框架
1. 本项目基于@uni-helper/vite-plugin-uni-layouts实现 uni-app 的可定制布局框架。
2. 可以结合 vite-plugin-uni-pages 的 route-block 实现指定页面布局。


## 路由跳转
1. 本项目使用uni-mini-router实现路由跳转。
2. uni-mini-router 提供2个hooks: `useRouter` 和 `useRoute`。
3. push方法 `router.push(target:RouteLocationRaw): void` 保留当前页面，跳转到应用内的某个页面，相当于使用 `uni.navigateTo(OBJECT)`。
4. pushTab方法 `router.pushTab(target:RouteLocationRaw): void` 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面，相当于使用 `uni.switchTab(OBJECT)`。
5. replace方法 `router.replace(target:RouteLocationRaw): void` 关闭当前页面，跳转到应用内的某个页面，相当于使用 `uni.redirectTo(OBJECT)`。
6. replaceAll方法 `router.replaceAll(target:RouteLocationRaw): void` 关闭所有页面，打开到应用内的某个页面，相当于使用 `uni.reLaunch(OBJECT)`。
7. back方法 `router.back(level?: number): void` 关闭当前页面，返回上一页面或多级页面，相当于使用 `uni.navigateBack(OBJECT)`。
